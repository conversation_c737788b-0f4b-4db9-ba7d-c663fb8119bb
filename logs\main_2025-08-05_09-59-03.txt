
使用命令行指定的配置文件: 13.json
使用指定的配置文件：13.json
已加载配置文件：batch_configs\13.json

处理第 1 个配置:
  应用默认值: round2批改模式 = 2
  应用默认值: 模型ID = 1
  应用默认值: response_format = 1
  应用默认值: 图像文件夹 = 1
  应用默认值: 像素增强 = n
  应用默认值: 像素粘连 = n
  应用默认值: 图像放大倍数 = 1
  ✓ 配置 1 验证通过

处理第 2 个配置:
  应用默认值: 模型ID = 1
  应用默认值: response_format = 1
  应用默认值: 图像文件夹 = 1
  应用默认值: 像素增强 = n
  应用默认值: 像素粘连 = n
  应用默认值: 图像放大倍数 = 1
  ✓ 配置 2 验证通过

处理第 3 个配置:
  应用默认值: 模型ID = 1
  应用默认值: response_format = 1
  应用默认值: 图像文件夹 = 1
  应用默认值: 像素增强 = n
  应用默认值: 像素粘连 = n
  应用默认值: 图像放大倍数 = 1
  ✓ 配置 3 验证通过

处理第 4 个配置:
  应用默认值: round2批改模式 = 2
  应用默认值: 模型ID = 1
  应用默认值: response_format = 1
  应用默认值: 图像文件夹 = 1
  应用默认值: 像素增强 = n
  应用默认值: 像素粘连 = n
  应用默认值: 图像放大倍数 = 1
  ✓ 配置 4 验证通过

有效配置数量: 4/4

检查是否需要创建配置副本...
配置中没有md格式的prompt，无需创建副本
无需创建配置副本
像素增强为'n'，忽略灰度阀门参数
使用模型: doubao-seed-1-6-250615
使用外部传入的图片文件夹：types\shuxueyingyongti\images
实际图片路径前缀：/images

使用路径：
图片文件夹：types\shuxueyingyongti\images
one_stage_response文件夹：types\shuxueyingyongti\one_stage_response
one_stage_prompt文件：types\shuxueyingyongti\one_stage_prompt.md
answer文件：types\shuxueyingyongti\response\answer.md
one_stage_error文件夹：types\shuxueyingyongti\one_stage_error
已从文件 types\shuxueyingyongti\one_stage_prompt.md 读取one_stage_prompt
已将markdown格式转换为纯文本
正在提取answer.md文档中的JSON响应...
从answer.md文档中提取到 245 个JSON响应
找到 245 张图片，开始逐个处理...
使用的one_stage_prompt: 请判断学生答案与下方正确答案是否一致，必须按照如下JSON格式识别： {"题目1": true, "题目2": false, "题目3": true} ，当学生回答与下方的正确答案一致时，该题目为 true，否则为 false，识别的JSON题号必须始终从"题目1"开始，依次递增。

--- 开始本地处理图片（增强/缩放/编码） ---

正在使用 20 个进程进行本地图片处理...
